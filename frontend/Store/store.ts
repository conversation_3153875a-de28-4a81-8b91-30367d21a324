// Zustand状态管理文件
// 此文件用于管理应用的全局状态

import { create } from 'zustand';

interface ButtonState {
  modeActive: boolean;
  businessActive: boolean;
  setModeActive: (active: boolean) => void;
  setBusinessActive: (active: boolean) => void;
}

export const useStore = create<ButtonState>((set) => ({
  modeActive: true, // 默认激活模式按键
  businessActive: false,
  setModeActive: (active: boolean) => set({ modeActive: active }),
  setBusinessActive: (active: boolean) => set({ businessActive: active }),
}));
