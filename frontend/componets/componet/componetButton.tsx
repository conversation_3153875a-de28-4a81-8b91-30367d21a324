import React from 'react';
import SecondaryButton from '../../ButtonCodes/secondary/SecondaryButton/SecondaryButton';
import { useStore } from '../../Store/store';

const ComponetButton: React.FC = () => {
  const { modeActive, businessActive, setModeActive, setBusinessActive } = useStore();

  const handleModeClick = () => {
    if (!modeActive) {
      setModeActive(true);
      setBusinessActive(false);
    }
  };

  const handleBusinessClick = () => {
    if (!businessActive) {
      setBusinessActive(true);
      setModeActive(false);
    }
  };

  return (
    <div style={{
      height: '3vh',
      width: '20vw',
      backgroundColor: 'transparent',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      position: 'absolute',
      top: '0',
      left: '0'
    }}>
      <SecondaryButton
        mode="toggle"
        isActive={modeActive}
        text="模式"
        onClick={modeActive ? undefined : handleModeClick}
        style={{
          height: '100%',
          width: '50%',
          color: '#0d0d0d',
          fontSize: 'auto',
          cursor: modeActive ? 'not-allowed' : 'pointer'
        }}
      />
      <SecondaryButton
        mode="toggle"
        isActive={businessActive}
        text="业务"
        onClick={businessActive ? undefined : handleBusinessClick}
        style={{
          height: '100%',
          width: '50%',
          color: '#0d0d0d',
          fontSize: 'auto',
          cursor: businessActive ? 'not-allowed' : 'pointer'
        }}
      />
    </div>
  );
};

export default ComponetButton;
