import { CSSProperties } from 'react';
import { secondaryButtonStyles } from '../style/style_secondary';

// 按键模式类型
export type ButtonMode = 'toggle' | 'instant';

// 按键状态接口
export interface ButtonState {
  isActive: boolean;
  isHovered: boolean;
  isPressed: boolean;
}

// 按键事件处理器接口
export interface ButtonEventHandlers {
  onClick?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onMouseDown?: () => void;
  onMouseUp?: () => void;
}

// 按键属性接口
export interface SecondaryButtonProps {
  mode?: ButtonMode;
  isActive?: boolean;
  text?: string;
  onClick?: () => void;
  className?: string;
  style?: CSSProperties;
}

// 获取按键样式的函数
export const getButtonStyle = (
  mode: ButtonMode,
  state: ButtonState
): CSSProperties => {
  const baseStyle = { ...secondaryButtonStyles.base };
  
  if (mode === 'toggle') {
    // 持续状态模式
    if (state.isActive) {
      if (state.isHovered) {
        baseStyle.backgroundColor = secondaryButtonStyles.toggle.activeHover.backgroundColor;
      } else {
        baseStyle.backgroundColor = secondaryButtonStyles.toggle.active.backgroundColor;
      }
    } else {
      if (state.isHovered) {
        baseStyle.backgroundColor = secondaryButtonStyles.toggle.inactiveHover.backgroundColor;
      } else {
        baseStyle.backgroundColor = secondaryButtonStyles.toggle.inactive.backgroundColor;
      }
    }
  } else if (mode === 'instant') {
    // 瞬时状态模式
    if (state.isPressed) {
      baseStyle.backgroundColor = secondaryButtonStyles.instant.pressed.backgroundColor;
    } else if (state.isHovered) {
      baseStyle.backgroundColor = secondaryButtonStyles.instant.hover.backgroundColor;
    } else {
      baseStyle.backgroundColor = secondaryButtonStyles.instant.default.backgroundColor;
    }
  }
  
  return baseStyle;
};

// 获取文字样式的函数
export const getTextStyle = (): CSSProperties => {
  return {
    fontSize: secondaryButtonStyles.text.fontSize,
    color: secondaryButtonStyles.text.color,
    textAlign: secondaryButtonStyles.text.textAlign as 'center',
    margin: 0,
    padding: 0,
  };
};

// 创建事件处理器的工厂函数
export const createButtonEventHandlers = (
  mode: ButtonMode,
  isActive: boolean,
  setIsActive: (active: boolean) => void,
  setIsHovered: (hovered: boolean) => void,
  setIsPressed: (pressed: boolean) => void,
  onClickCallback?: () => void
): ButtonEventHandlers => {
  return {
    onClick: () => {
      if (mode === 'toggle') {
        setIsActive(!isActive);
      }
      onClickCallback?.();
    },
    onMouseEnter: () => {
      setIsHovered(true);
    },
    onMouseLeave: () => {
      setIsHovered(false);
      if (mode === 'instant') {
        setIsPressed(false);
      }
    },
    onMouseDown: () => {
      if (mode === 'instant') {
        setIsPressed(true);
      }
    },
    onMouseUp: () => {
      if (mode === 'instant') {
        setIsPressed(false);
      }
    },
  };
};
