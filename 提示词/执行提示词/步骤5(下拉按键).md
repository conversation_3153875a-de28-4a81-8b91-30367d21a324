# 普通按键

## 初始提示词

- 1.阅读‘下拉按键路径.md’并用于创建文件
- 2.阅读‘前端技术栈.md’并用于构建代码
- 3.严格执行‘下拉按键.md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.更新.gitignore文档

## 优化提示词

请按照以下步骤顺序执行下拉按键组件的开发任务：

1. **文件路径规划**：首先阅读 `下拉按键路径.md` 文件，了解需要创建的文件结构和路径规范，并严格按照该文档中的路径要求创建相应的文件和文件夹
2. **技术栈确认**：阅读 `前端技术栈.md` 文件，了解项目使用的前端技术栈（如React、TypeScript、样式框架等），确保代码实现符合项目的技术规范
3. **功能实现**：严格按照 `下拉按键.md` 文件中的具体功能要求和实现指示进行开发，不得偏离文档中的规格说明
4. **文档限制**：在执行过程中，除了上述三个指定的提示词文档外，不得提前阅读其他任何文档或参考资料
5. **范围控制**：仅完成当前任务中明确要求的下拉按键功能逻辑，不得自行添加额外功能或进行功能扩展
6. **项目配置更新**：完成代码开发后，检查并更新项目根目录下的 `.gitignore` 文件，确保新增的文件符合版本控制规范
7. **功能测试**：实现完成后，编写并执行相应的测试用例，验证普通按键功能的正确性
注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步
