# 项目文件

## 声明

- 1.若路径不存在则创建路径文件

## 创建项目文件

- 1.创建项目主文件夹‘frontend’
  - 1.创建按键仓库文件夹‘ButtonCodes’
  - 2.创建页面路由文件夹'app'
  - 3.创建通用组件文件夹'componets'
    - 1.创建二级组件文件夹‘componet’
    - 2.创建二级组件交互逻辑文件夹‘interaction’
  - 4.创建功能模块文件夹'features'
    - 1.创建功能模块组件文件夹‘feature’
    - 2.创建功能模块逻辑文件夹‘logic’
  - 5.创建状态管理文件夹‘Store’
    - 1.创建状态管理文件‘store.ts’

## 创建文本类文件

- 1.创建文本类主文件夹‘docs’
  - 1.用于存放报告，分析，指南类文档文件夹‘report’

## 创建测试类文件

- 1.创建测试类主文件夹‘apps’
  - 1.创建前端测试文件夹‘frontend’
    - 1.创建测试脚本存储统一文件夹‘scripts’
    - 2.创建前端单元测试统一文件夹‘test’
    - 3.创建storybook管理统一文件夹‘stories’
  - 2.创建后端测试文件夹‘backend’
    - 1.创建后端测试统一文件夹‘test’
