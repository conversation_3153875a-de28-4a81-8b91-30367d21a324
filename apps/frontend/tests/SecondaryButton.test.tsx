import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SecondaryButton } from '../ButtonCodes/secondary/SecondaryButton/SecondaryButton';

describe('SecondaryButton', () => {
  // 测试基本渲染
  test('renders with default props', () => {
    render(<SecondaryButton />);
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('按键');
  });

  // 测试自定义文本
  test('renders with custom text', () => {
    render(<SecondaryButton text="自定义按键" />);
    const button = screen.getByRole('button');
    expect(button).toHaveTextContent('自定义按键');
  });

  // 测试持续状态模式（toggle）
  describe('Toggle mode', () => {
    test('toggles active state on click', () => {
      render(<SecondaryButton mode="toggle" />);
      const button = screen.getByRole('button');
      
      // 初始状态应该是未激活
      expect(button).toHaveStyle('background-color: #f1f1f1');
      
      // 点击后应该变为激活状态
      fireEvent.click(button);
      expect(button).toHaveStyle('background-color: #929292');
      
      // 再次点击应该回到未激活状态
      fireEvent.click(button);
      expect(button).toHaveStyle('background-color: #f1f1f1');
    });

    test('shows hover effect in inactive state', () => {
      render(<SecondaryButton mode="toggle" />);
      const button = screen.getByRole('button');
      
      // 悬停时应该改变背景色
      fireEvent.mouseEnter(button);
      expect(button).toHaveStyle('background-color: #e4e4e4');
      
      // 离开悬停应该恢复原色
      fireEvent.mouseLeave(button);
      expect(button).toHaveStyle('background-color: #f1f1f1');
    });

    test('shows hover effect in active state', () => {
      render(<SecondaryButton mode="toggle" isActive={true} />);
      const button = screen.getByRole('button');
      
      // 激活状态下悬停
      fireEvent.mouseEnter(button);
      expect(button).toHaveStyle('background-color: #858585');
      
      // 离开悬停应该恢复激活状态色
      fireEvent.mouseLeave(button);
      expect(button).toHaveStyle('background-color: #929292');
    });
  });

  // 测试瞬时状态模式（instant）
  describe('Instant mode', () => {
    test('shows pressed effect on mouse down', () => {
      render(<SecondaryButton mode="instant" />);
      const button = screen.getByRole('button');
      
      // 初始状态
      expect(button).toHaveStyle('background-color: #f1f1f1');
      
      // 按下时应该显示按下效果
      fireEvent.mouseDown(button);
      expect(button).toHaveStyle('background-color: #858585');
      
      // 松开时应该恢复
      fireEvent.mouseUp(button);
      expect(button).toHaveStyle('background-color: #f1f1f1');
    });

    test('shows hover effect', () => {
      render(<SecondaryButton mode="instant" />);
      const button = screen.getByRole('button');
      
      // 悬停时应该改变背景色
      fireEvent.mouseEnter(button);
      expect(button).toHaveStyle('background-color: #e4e4e4');
      
      // 离开悬停应该恢复
      fireEvent.mouseLeave(button);
      expect(button).toHaveStyle('background-color: #f1f1f1');
    });

    test('resets pressed state on mouse leave', () => {
      render(<SecondaryButton mode="instant" />);
      const button = screen.getByRole('button');
      
      // 按下
      fireEvent.mouseDown(button);
      expect(button).toHaveStyle('background-color: #858585');
      
      // 离开按键区域应该重置按下状态
      fireEvent.mouseLeave(button);
      expect(button).toHaveStyle('background-color: #f1f1f1');
    });
  });

  // 测试点击回调
  test('calls onClick callback when clicked', () => {
    const mockOnClick = jest.fn();
    render(<SecondaryButton onClick={mockOnClick} />);
    const button = screen.getByRole('button');
    
    fireEvent.click(button);
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  // 测试自定义样式
  test('applies custom styles', () => {
    const customStyle = { width: '300px', height: '60px' };
    render(<SecondaryButton style={customStyle} />);
    const button = screen.getByRole('button');
    
    expect(button).toHaveStyle('width: 300px');
    expect(button).toHaveStyle('height: 60px');
  });

  // 测试自定义类名
  test('applies custom className', () => {
    render(<SecondaryButton className="custom-button" />);
    const button = screen.getByRole('button');
    
    expect(button).toHaveClass('custom-button');
  });
});
