// 普通按键样式定义
export interface SecondaryButtonStyles {
  // 基础样式
  base: {
    width: string;
    height: string;
    borderRadius: string;
    backgroundColor: string;
    display: string;
    justifyContent: string;
    alignItems: string;
    cursor: string;
    border: string;
    outline: string;
    transition: string;
  };
  // 文字样式
  text: {
    fontSize: string;
    color: string;
    textAlign: string;
  };
  // 持续状态模式样式
  toggle: {
    active: {
      backgroundColor: string;
    };
    activeHover: {
      backgroundColor: string;
    };
    inactive: {
      backgroundColor: string;
    };
    inactiveHover: {
      backgroundColor: string;
    };
  };
  // 瞬时状态模式样式
  instant: {
    default: {
      backgroundColor: string;
    };
    hover: {
      backgroundColor: string;
    };
    pressed: {
      backgroundColor: string;
    };
  };
}

// 默认样式配置
export const secondaryButtonStyles: SecondaryButtonStyles = {
  base: {
    width: '200px',
    height: '50px',
    borderRadius: '0px',
    backgroundColor: '#f1f1f1',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    border: 'none',
    outline: 'none',
    transition: 'background-color 0.2s ease',
  },
  text: {
    fontSize: '20px',
    color: '#242424',
    textAlign: 'center',
  },
  toggle: {
    active: {
      backgroundColor: '#929292',
    },
    activeHover: {
      backgroundColor: '#858585',
    },
    inactive: {
      backgroundColor: '#f1f1f1',
    },
    inactiveHover: {
      backgroundColor: '#e4e4e4',
    },
  },
  instant: {
    default: {
      backgroundColor: '#f1f1f1',
    },
    hover: {
      backgroundColor: '#e4e4e4',
    },
    pressed: {
      backgroundColor: '#858585',
    },
  },
};
