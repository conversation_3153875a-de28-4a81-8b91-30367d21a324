'use client';

import React, { useState, useEffect } from 'react';
import {
  SecondaryButtonProps,
  ButtonState,
  getButtonStyle,
  getTextStyle,
  createButtonEventHandlers,
} from '../event/event_secondary';

// 普通按键组件
export const SecondaryButton: React.FC<SecondaryButtonProps> = ({
  mode = 'toggle', // 默认为持续状态模式
  isActive: externalIsActive = false,
  text = '按键', // 默认文本
  onClick,
  className,
  style,
}) => {
  // 内部状态管理
  const [internalIsActive, setInternalIsActive] = useState(externalIsActive);
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  // 当外部 isActive 改变时，更新内部状态
  useEffect(() => {
    setInternalIsActive(externalIsActive);
  }, [externalIsActive]);

  // 构建按键状态对象
  const buttonState: ButtonState = {
    isActive: internalIsActive,
    isHovered,
    isPressed,
  };

  // 创建事件处理器
  const eventHandlers = createButtonEventHandlers(
    mode,
    internalIsActive,
    setInternalIsActive,
    setIsHovered,
    setIsPressed,
    onClick
  );

  // 获取样式
  const buttonStyle = getButtonStyle(mode, buttonState);
  const textStyle = getTextStyle();

  // 合并自定义样式
  const finalButtonStyle = {
    ...buttonStyle,
    ...style,
  };

  return (
    <button
      className={className}
      style={finalButtonStyle}
      onClick={eventHandlers.onClick}
      onMouseEnter={eventHandlers.onMouseEnter}
      onMouseLeave={eventHandlers.onMouseLeave}
      onMouseDown={eventHandlers.onMouseDown}
      onMouseUp={eventHandlers.onMouseUp}
      type="button"
    >
      <span style={textStyle}>{text}</span>
    </button>
  );
};

export default SecondaryButton;
